const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn, exec } = require('child_process');
const RuntimeErrorHandler = require('../runtime-inject/RuntimeErrorHandler');
const BrowserDetector = require('./BrowserDetector');
const BuildFixAgent = require('../build-fix/ai/BuildFixAgent');
const BuildErrorAnalyzer = require('../build-fix/ai/BuildErrorAnalyzer');
const AutoLoginManager = require('./login/AutoLoginManager');
const FAQHelper = require('./FAQHelper');
const ErrorAnalyzer = require('./ErrorAnalyzer');
const { browserFactory } = require('../../infrastructure/browser');

/**
 * PageValidator - 页面运行时验证器
 *
 * 功能：
 * 1. 启动开发服务器
 * 2. 使用 Puppeteer 访问每个路由页面
 * 3. 捕获页面错误和控制台输出
 * 4. 集成 RuntimeErrorHandler 进行错误处理
 * 5. 生成验证报告
 */
class PageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.allRoutes = routes || [];
    this.routeParser = options.routeParser || null; // 路由解析器实例

    // 过滤路由（如果指定了特定路由）
    if (options.specificRoutes && options.specificRoutes.length > 0) {
      this.routes = this.allRoutes.filter(route =>
        options.specificRoutes.includes(route.path)
      );
      console.log(chalk.yellow(`🎯 只验证指定的 ${this.routes.length} 个路由: ${options.specificRoutes.join(', ')}`));
    } else {
      this.routes = this.allRoutes;
    }
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new', // 使用新的 headless 模式
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      maxFixAttempts: 3, // 最大修复尝试次数
      revalidateAfterFix: true, // 修复后重新验证页面
      dryRun: false, // 预览模式，不实际修改文件
      waitForServer: 60000,  // 等待服务器启动时间
      pageTimeout: 15000,   // 增加页面超时时间到15秒
      navigationTimeout: 20000, // 导航超时时间
      executablePath: null, // 浏览器可执行文件路径
      routerMode: 'hash', // Vue Router模式: 'hash' 或 'history'
      loginCredentials: {   // 登录凭据
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,     // 是否跳过自动登录
      ...options
    };

    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.devServer = null;
    this.browser = null;
    this.browserAutomation = null;
    this.validationResults = [];
    this.errors = [];
    this.isLoggedIn = false; // 登录状态标记

    // 浏览器检测器
    this.browserDetector = new BrowserDetector({
      verbose: this.options.verbose,
      preferredBrowsers: ['chrome', 'chromium', 'edge']
    });

    // 集成运行时错误处理器
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });
    }

    // 集成 BuildFixAgent 用于页面错误修复
    if (this.options.autoFix) {
      this.buildFixAgent = new BuildFixAgent(projectPath, {
        maxAttempts: this.options.maxFixAttempts || 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun || false
      });
    }

    // 初始化自动登录管理器
    this.autoLoginManager = new AutoLoginManager({
      username: this.options.loginCredentials?.username || this.options.username || 'admin',
      password: this.options.loginCredentials?.password || this.options.password || '111111',
      verbose: this.options.verbose,
      aiEnabled: !this.options.skipLogin,
      configPath: path.join(projectPath, '.login-config.json')
    });

    // 初始化 FAQ 助手
    this.faqHelper = new FAQHelper(projectPath, {
      verbose: this.options.verbose
    });

    // 初始化错误分析器
    this.errorAnalyzer = new ErrorAnalyzer({
      verbose: this.options.verbose
    });

    // 初始化构建错误分析器（用于统一的错误输出处理）
    this.buildErrorAnalyzer = new BuildErrorAnalyzer(projectPath, null, null, {
      verbose: this.options.verbose
    });
  }

  /**
   * 验证所有页面 - 优化版本
   * 1. 优先验证首页 (/)
   * 2. 分批处理页面 (每批10个)
   * 3. 在登录页面也读取console信息
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    if (this.options.verbose) {
      console.log(chalk.gray(`   详细模式已启用`));
      console.log(chalk.gray(`   页面超时: ${this.options.pageTimeout}ms`));
      console.log(chalk.gray(`   导航超时: ${this.options.navigationTimeout}ms`));
      console.log(chalk.gray(`   自动修复: ${this.options.autoFix ? '启用' : '禁用'}`));
      console.log(chalk.gray(`   路由模式: ${this.options.routerMode}`));
    }

    try {
      // 0. 初始化 FAQ 系统
      await this.faqHelper.initialize();

      // 1. 启动开发服务器
      await this.startDevServer();

      // 2. 启动浏览器
      await this.startBrowser();

      // 3. 优化的页面验证流程
      await this.validatePagesOptimized();

      // 4. 生成报告
      const report = this.generateReport();

      console.log(chalk.green(`✅ 页面验证完成`));
      this.printSummary();

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 优化的页面验证流程
   * 1. 优先验证首页 (/)
   * 2. 分批处理其他页面 (每批10个)
   */
  async validatePagesOptimized() {
    // 1. 首先验证首页
    const homePageResult = await this.validateHomePage();

    // 如果首页有错误且启用了自动修复，优先修复首页
    if (!homePageResult.success && this.options.autoFix) {
      console.log(chalk.yellow(`🏠 首页存在问题，优先修复...`));
      await this.fixHomePageIfNeeded(homePageResult);
    }

    // 2. 获取剩余的路由（排除首页）
    const remainingRoutes = this.routes.filter(route => route.path !== '/');

    if (remainingRoutes.length === 0) {
      console.log(chalk.green(`✅ 只有首页需要验证，验证完成`));
      return;
    }

    // 3. 分批处理剩余页面
    const batchSize = 10;
    const batches = this.createBatches(remainingRoutes, batchSize);

    console.log(chalk.blue(`📦 将验证 ${remainingRoutes.length} 个剩余页面，分为 ${batches.length} 批处理`));

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(chalk.blue(`\n📋 处理第 ${batchIndex + 1}/${batches.length} 批 (${batch.length} 个页面)`));

      await this.validateBatch(batch, batchIndex);

      // 批次间短暂休息
      if (batchIndex < batches.length - 1) {
        console.log(chalk.gray(`   ⏸️  批次间休息 500ms...`));
        await this.sleep(500);
      }
    }
  }

  /**
   * 验证首页
   */
  async validateHomePage() {
    const homeRoute = this.routes.find(route => route.path === '/');

    if (!homeRoute) {
      console.log(chalk.yellow(`⚠️  未找到首页路由 (/)`));
      return { success: true, route: null };
    }

    console.log(chalk.blue(`🏠 优先验证首页: ${homeRoute.path}`));

    const result = await this.validateSinglePageWithConsoleCapture(homeRoute);
    this.validationResults.push(result);

    this.displayPageResult(result, 1, 1);

    return result;
  }

  /**
   * 修复首页问题（如果需要）
   */
  async fixHomePageIfNeeded(homePageResult) {
    if (homePageResult.success || !homePageResult.errors || homePageResult.errors.length === 0) {
      return;
    }

    console.log(chalk.yellow(`🔧 尝试修复首页的 ${homePageResult.errors.length} 个错误...`));

    // 显示首页错误详情
    this.errorAnalyzer.displayErrorSummary(homePageResult.errors, 5);

    const fixResult = await this.attemptPageErrorFix(homePageResult.route, homePageResult.errors);

    if (fixResult.success) {
      console.log(chalk.green(`✅ 首页错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
      homePageResult.fixAttempted = true;
      homePageResult.fixResult = fixResult;

      // 重新验证首页
      if (this.options.revalidateAfterFix) {
        console.log(chalk.gray(`🔄 重新验证首页...`));
        const revalidationResult = await this.validateSinglePageWithConsoleCapture(homePageResult.route);

        if (revalidationResult.success) {
          console.log(chalk.green(`✅ 首页修复后验证成功`));
          // 更新结果
          Object.assign(homePageResult, revalidationResult);
        } else {
          console.log(chalk.yellow(`⚠️  首页修复后仍有问题: ${revalidationResult.errors.length} 个错误`));
        }
      }
    } else {
      console.log(chalk.red(`❌ 首页错误修复失败: ${fixResult.error || '未知原因'}`));
    }
  }

  /**
   * 创建批次
   */
  createBatches(routes, batchSize) {
    const batches = [];
    for (let i = 0; i < routes.length; i += batchSize) {
      batches.push(routes.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 验证一个批次的页面
   */
  async validateBatch(batch, batchIndex) {
    for (let i = 0; i < batch.length; i++) {
      const route = batch[i];
      const globalIndex = this.validationResults.length + 1;
      const totalRoutes = this.routes.length;

      console.log(chalk.gray(`   [${globalIndex}/${totalRoutes}] 验证页面: ${route.path}`));

      const result = await this.validateSinglePageWithConsoleCapture(route);
      this.validationResults.push(result);

      this.displayPageResult(result, globalIndex, totalRoutes);

      // 页面间短暂延迟
      await this.sleep(100);
    }
  }

  /**
   * 显示页面验证结果
   */
  displayPageResult(result, currentIndex, totalRoutes) {
    if (result.success) {
      console.log(chalk.green(`   ✅ 成功`));
      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        if (result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
      }
    } else {
      console.log(chalk.red(`   ❌ 失败: ${result.errors.length} 个错误`));

      // 在verbose模式下显示更多信息
      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        console.log(chalk.gray(`      URL: ${result.url}`));
        if (result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
        if (result.needsLogin) {
          console.log(chalk.yellow(`      需要登录: ${result.loginAttempted ? '已尝试' : '未尝试'}`));
        }
      }

      // 显示错误摘要（如果之前没有显示过且不是自动修复模式）
      if (!this.options.autoFix || !this.buildFixAgent) {
        this.errorAnalyzer.displayErrorSummary(result.errors, 2);
      }

      if (result.fixAttempted) {
        const fixStatus = result.fixResult?.success ? '成功' : '失败';
        console.log(chalk.yellow(`   🔧 已尝试自动修复: ${fixStatus}`));

        if (this.options.verbose && result.fixResult) {
          if (result.fixResult.success) {
            console.log(chalk.gray(`      修复文件: ${result.fixResult.filesModified || 0} 个`));
          } else {
            console.log(chalk.gray(`      修复失败原因: ${result.fixResult.error || '未知'}`));
          }
        }
      }
    }
  }

  /**
   * 验证单个页面并捕获控制台信息（包括登录时的错误）
   */
  async validateSinglePageWithConsoleCapture(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      navigationHistory: [], // 新增：页面跳转历史记录
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false,
      loginConsoleErrors: [] // 新增：登录时的控制台错误
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 在页面导航之前立即设置所有监听器
      this.setupPageListenersWithLoginCapture(page, result);

      // 设置页面错误监听
      page.on('pageerror', (error) => {
        const errorMessage = error.message;
        result.errors.push({
          type: 'page-error',
          message: errorMessage,
          timestamp: new Date().toISOString(),
          stack: error.stack
        });

        if (this.options.verbose) {
          console.log(chalk.red(`      🔴 Page Error: ${errorMessage.substring(0, 150)}...`));
        }

        // 检查是否是路由错误
        if (this.isRouteError(errorMessage)) {
          result.loginConsoleErrors.push({
            type: 'page-route-error',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });
        }
      });

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.navigationTimeout);

      // 访问页面并捕获登录阶段的错误
      const navigationResult = await this.navigateWithLoginErrorCapture(page, url, result);

      if (!navigationResult.success) {
        result.errors.push(...navigationResult.errors);
        result.loadTime = Date.now() - startTime;
        await page.close();
        return result;
      }

      // 继续原有的验证逻辑...
      return await this.continuePageValidation(page, result, startTime);

    } catch (error) {
      result.errors.push({
        type: 'validation-error',
        message: `页面验证异常: ${error.message}`,
        stack: error.stack
      });
      result.loadTime = Date.now() - (result.startTime || Date.now());
      return result;
    }
  }

  /**
   * 设置页面监听器（包括登录阶段的控制台捕获和页面跳转检测）
   */
  setupPageListenersWithLoginCapture(page, result) {
    this.setupNavigationTracking(page, result);

    // 控制台消息监听（包括登录阶段）
    page.on('console', (msg) => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 在详细模式下显示所有console消息
      if (this.options.verbose) {
        const msgType = msg.type();
        const msgText = msg.text();

        if (msgType === 'error') {
          console.log(chalk.red(`      🔴 Console Error: ${msgText.substring(0, 150)}${msgText.length > 150 ? '...' : ''}`));
        } else if (msgType === 'warning') {
          // console.log(chalk.yellow(`      🟡 Console Warning: ${msgText.substring(0, 100)}${msgText.length > 100 ? '...' : ''}`));
        } else if (msgType === 'log' && msgText.includes('error')) {
          console.log(chalk.gray(`      ⚪ Console Log (error-like): ${msgText.substring(0, 100)}${msgText.length > 100 ? '...' : ''}`));
        }
      }

      // 捕获错误级别的控制台消息
      if (msg.type() === 'error') {
        const errorText = msg.text();

        // 检查是否是路由相关错误
        if (this.isRouteError(errorText)) {
          result.loginConsoleErrors.push({
            type: 'route-error',
            message: errorText,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });

          if (this.options.verbose) {
            console.log(chalk.red(`      🚨 路由错误检测到: ${errorText.substring(0, 200)}...`));
          }
        }

        // 其他错误也记录
        result.errors.push({
          type: 'console-error',
          message: errorText,
          timestamp: new Date().toISOString(),
          phase: result.loginAttempted ? 'post-login' : 'pre-login'
        });
      }

      // 也检查warning和log消息中是否包含路由错误信息
      if (msg.type() === 'warning' || msg.type() === 'log') {
        const messageText = msg.text();
        if (this.isRouteError(messageText)) {
          result.loginConsoleErrors.push({
            type: `${msg.type()}-route-error`,
            message: messageText,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });

          if (this.options.verbose) {
            console.log(chalk.yellow(`      🚨 路由错误(${msg.type()}): ${messageText.substring(0, 200)}...`));
          }
        }
      }
    });

    // 页面错误监听
    page.on('pageerror', (error) => {
      result.errors.push({
        type: 'page-error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        phase: result.loginAttempted ? 'post-login' : 'pre-login'
      });
    });

    // 网络错误监听
    page.on('requestfailed', (request) => {
      result.networkErrors.push({
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error',
        timestamp: new Date().toISOString(),
        phase: result.loginAttempted ? 'post-login' : 'pre-login'
      });
    });
  }

  /**
   * 设置页面跳转检测
   */
  setupNavigationTracking(page, result) {
    // 初始化跳转记录
    if (!result.navigationHistory) {
      result.navigationHistory = [];
    }

    // 记录初始URL
    const initialUrl = page.url();
    if (initialUrl && initialUrl !== 'about:blank') {
      result.navigationHistory.push({
        url: initialUrl,
        timestamp: new Date().toISOString(),
        type: 'initial',
        reason: 'page_load'
      });
    }

    // 监听页面跳转事件 - 使用通用的 load 事件
    let lastUrl = initialUrl;

    page.on('load', () => {
      const currentUrl = page.url();
      const timestamp = new Date().toISOString();

      // 如果URL发生变化，记录跳转
      if (currentUrl !== lastUrl && currentUrl !== 'about:blank') {
        const navigationEntry = {
          url: currentUrl,
          timestamp: timestamp,
          type: 'navigation',
          reason: 'page_load'
        };

        result.navigationHistory.push(navigationEntry);

        // 输出跳转日志
        if (this.options.verbose) {
          console.log(chalk.cyan(`    🔄 页面跳转检测: ${currentUrl}`));
        }

        // 检查是否是重定向到登录页面
        if (this.isLoginRedirect(currentUrl)) {
          navigationEntry.isLoginRedirect = true;
          if (this.options.verbose) {
            console.log(chalk.yellow(`    🔐 检测到登录页面重定向: ${currentUrl}`));
          }
        }

        // 检查是否是错误页面
        if (this.isErrorPage(currentUrl)) {
          navigationEntry.isErrorPage = true;
          if (this.options.verbose) {
            console.log(chalk.red(`    ❌ 检测到错误页面跳转: ${currentUrl}`));
          }
        }

        lastUrl = currentUrl;
      }
    });

    // 监听页面响应事件（用于检测HTTP重定向）
    page.on('response', (response) => {
      const status = response.status();
      const url = response.url();

      // 检测重定向状态码
      if (status >= 300 && status < 400) {
        const timestamp = new Date().toISOString();
        const redirectEntry = {
          url: url,
          timestamp: timestamp,
          type: 'redirect',
          reason: `http_${status}`,
          statusCode: status,
          statusText: response.statusText()
        };

        result.navigationHistory.push(redirectEntry);

        if (this.options.verbose) {
          console.log(chalk.magenta(`    ↩️  HTTP重定向检测: ${status} ${url}`));
        }
      }
    });
  }

  /**
   * 检查是否是登录重定向
   */
  isLoginRedirect(url) {
    const loginPatterns = [
      /\/login/i,
      /\/signin/i,
      /\/auth/i,
      /\/authentication/i,
      /\/sso/i
    ];

    return loginPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 检查是否是错误页面
   */
  isErrorPage(url) {
    const errorPatterns = [
      /\/error/i,
      /\/404/i,
      /\/403/i,
      /\/500/i,
      /\/not-found/i,
      /\/unauthorized/i,
      /\/forbidden/i
    ];

    return errorPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 输出页面跳转历史日志
   */
  logNavigationHistory(result) {
    if (!result.navigationHistory || result.navigationHistory.length === 0) {
      return;
    }

    // 统计跳转信息
    const totalNavigations = result.navigationHistory.length;
    const redirects = result.navigationHistory.filter(nav => nav.type === 'redirect').length;
    const loginRedirects = result.navigationHistory.filter(nav => nav.isLoginRedirect).length;
    const errorPages = result.navigationHistory.filter(nav => nav.isErrorPage).length;

    // 如果有跳转，输出概要信息
    if (totalNavigations > 1) {
      console.log(chalk.blue(`    🔄 页面跳转检测: 共发生 ${totalNavigations - 1} 次跳转`));

      if (redirects > 0) {
        console.log(chalk.yellow(`    ↩️  HTTP重定向: ${redirects} 次`));
      }

      if (loginRedirects > 0) {
        console.log(chalk.cyan(`    🔐 登录页面重定向: ${loginRedirects} 次`));
      }

      if (errorPages > 0) {
        console.log(chalk.red(`    ❌ 错误页面跳转: ${errorPages} 次`));
      }

      // 详细跳转历史（仅在 verbose 模式下显示）
      if (this.options.verbose) {
        console.log(chalk.gray(`    📋 跳转历史详情:`));
        result.navigationHistory.forEach((nav, index) => {
          const time = new Date(nav.timestamp).toLocaleTimeString();
          let icon = '🔗';
          let color = chalk.gray;

          if (nav.type === 'initial') {
            icon = '🏠';
            color = chalk.blue;
          } else if (nav.type === 'redirect') {
            icon = '↩️';
            color = chalk.yellow;
          } else if (nav.isLoginRedirect) {
            icon = '🔐';
            color = chalk.cyan;
          } else if (nav.isErrorPage) {
            icon = '❌';
            color = chalk.red;
          }

          const statusInfo = nav.statusCode ? ` (${nav.statusCode})` : '';
          console.log(color(`      ${index + 1}. ${icon} ${time} - ${nav.url}${statusInfo}`));
        });
      }
    } else {
      // 没有跳转的情况
      if (this.options.verbose) {
        console.log(chalk.green(`    ✅ 页面无跳转，直接加载成功`));
      }
    }
  }

  /**
   * 检查是否是路由相关错误
   */
  isRouteError(errorText) {
    const routeErrorPatterns = [
      /Route paths should start with a "\/"/,
      /should be "\/.*"/,
      /Invalid route/,
      /Router.*error/i,
      /vue-router/i,
      /navigation.*error/i,
      /tokenizePath/i,
      /createRouteRecordMatcher/i,
      /addRoute/i
    ];

    return routeErrorPatterns.some(pattern => pattern.test(errorText));
  }

  /**
   * 导航到页面并捕获登录阶段的错误
   */
  async navigateWithLoginErrorCapture(page, url, result) {
    try {
      // 访问页面
      let response;
      let navigationAttempts = 0;
      const maxNavigationAttempts = 3;

      while (navigationAttempts < maxNavigationAttempts) {
        try {
          response = await page.goto(url, {
            waitUntil: ['domcontentloaded', 'networkidle0'],
            timeout: this.options.navigationTimeout
          });
          break;
        } catch (navError) {
          navigationAttempts++;
          if (navigationAttempts >= maxNavigationAttempts) {
            throw navError;
          }
          console.log(chalk.yellow(`    ⚠️  导航重试 ${navigationAttempts}/${maxNavigationAttempts}: ${navError.message}`));
          await this.sleep(1000);
        }
      }

      // 检查响应状态
      if (response && !response.ok()) {
        result.warnings.push(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // 等待页面初始化，给足够时间让路由错误显现
      if (this.options.verbose) {
        console.log(chalk.gray(`    ⏳ 等待页面初始化和路由处理...`));
      }
      await this.sleep(3000); // 增加等待时间到3秒

      // 检查导航后的URL是否发生了跳转
      const finalUrl = page.url();
      if (finalUrl !== url) {
        const navigationEntry = {
          url: finalUrl,
          timestamp: new Date().toISOString(),
          type: 'navigation',
          reason: 'post_navigation_redirect',
          originalUrl: url
        };

        result.navigationHistory.push(navigationEntry);

        if (this.options.verbose) {
          console.log(chalk.cyan(`    🔄 导航后检测到跳转: ${url} → ${finalUrl}`));
        }

        // 检查是否是重定向到登录页面
        if (this.isLoginRedirect(finalUrl)) {
          navigationEntry.isLoginRedirect = true;
          if (this.options.verbose) {
            console.log(chalk.yellow(`    🔐 检测到登录页面重定向: ${finalUrl}`));
          }
        }

        // 检查是否是错误页面
        if (this.isErrorPage(finalUrl)) {
          navigationEntry.isErrorPage = true;
          if (this.options.verbose) {
            console.log(chalk.red(`    ❌ 检测到错误页面跳转: ${finalUrl}`));
          }
        }
      }

      // 检查是否有早期的路由错误
      if (result.loginConsoleErrors.length > 0) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  检测到 ${result.loginConsoleErrors.length} 个早期路由错误`));
          for (const error of result.loginConsoleErrors) {
            console.log(chalk.red(`      - ${error.message.substring(0, 100)}...`));
          }
        }
      }

      const needsLogin = await this.checkIfNeedsLogin(page);
      result.needsLogin = needsLogin;

      if (needsLogin && !this.options.skipLogin) {
        console.log(chalk.yellow(`    🔐 检测到需要登录，尝试自动登录...`));
        result.loginAttempted = true;

        // 在登录前记录当前的控制台错误数量
        const preLoginErrorCount = result.loginConsoleErrors.length;

        const loginResult = await this.autoLoginManager.attemptLogin(page);

        // 登录后检查是否有新的控制台错误
        const postLoginErrorCount = result.loginConsoleErrors.length;
        if (postLoginErrorCount > preLoginErrorCount) {
          console.log(chalk.yellow(`    ⚠️  登录过程中检测到 ${postLoginErrorCount - preLoginErrorCount} 个控制台错误`));
        }

        if (loginResult.success) {
          console.log(chalk.green(`    ✅ 自动登录成功`));
          this.isLoggedIn = true;

          // 登录成功后等待页面稳定
          await this.sleep(2000);
        } else {
          console.log(chalk.red(`    ❌ 自动登录失败: ${loginResult.error}`));
          result.warnings.push(`自动登录失败: ${loginResult.error}`);
        }
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        errors: [{
          type: 'navigation-error',
          message: `页面导航失败: ${error.message}`,
          stack: error.stack,
          timestamp: new Date().toISOString()
        }]
      };
    }
  }

  /**
   * 检查页面是否需要登录
   */
  async checkIfNeedsLogin(page) {
    try {
      const currentUrl = page.url();

      // 检查URL是否包含登录相关路径
      const loginPaths = ['/login', '/signin', '/auth', '/authentication'];
      const isOnLoginPage = loginPaths.some(path => currentUrl.includes(path));

      // 如果在登录页面，说明需要登录
      if (isOnLoginPage) {
        return true;
      }

      // 检查页面是否有登录表单（可能是模态框登录）
      const hasLoginForm = await page.evaluate(() => {
        const loginSelectors = [
          '.login-form',
          '.login-container',
          'form[name="login"]',
          'form[id="login"]',
          'input[name="username"]',
          'input[name="email"]',
          'input[type="email"]',
          'input[placeholder*="用户名"]',
          'input[placeholder*="邮箱"]',
          'input[placeholder*="username"]',
          'input[placeholder*="email"]'
        ];

        return loginSelectors.some(selector => {
          try {
            return document.querySelector(selector) !== null;
          } catch (e) {
            return false;
          }
        });
      });

      // 检查是否有登录相关的文本或按钮
      const hasLoginIndicators = await page.evaluate(() => {
        const loginTexts = ['登录', '登陆', 'Login', 'Sign In', 'Sign in'];
        const bodyText = document.body.textContent || '';

        // 检查是否有登录按钮或链接
        const loginButtons = document.querySelectorAll('button, a, .btn');
        for (const button of loginButtons) {
          const buttonText = button.textContent || button.innerText || '';
          if (loginTexts.some(text => buttonText.includes(text))) {
            return true;
          }
        }

        // 检查页面文本是否包含登录提示
        return loginTexts.some(text => bodyText.includes(text));
      });

      return hasLoginForm || hasLoginIndicators;

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  检查登录状态失败: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 继续页面验证（原有逻辑的延续）
   */
  async continuePageValidation(page, result, startTime) {
    try {
      // 等待页面渲染和Vue应用初始化
      await this.waitForPageReady(page);

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 截图功能
      try {
        const screenshotPath = await this.takeScreenshot(page, result.route);
        result.screenshotPath = screenshotPath;
        if (this.options.verbose) {
          console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
        }
      } catch (screenshotError) {
        const errorMessage = `截图失败: ${screenshotError.message}`;
        result.warnings.push(errorMessage);
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  ${errorMessage}`));
        }
      }

      // 继续原有的错误检测逻辑...
      result.loadTime = Date.now() - startTime;

      // 输出页面跳转历史日志
      this.logNavigationHistory(result);

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      } else if (this.options.autoFix && this.buildFixAgent) {
        // 尝试自动修复
        const fixResult = await this.attemptPageErrorFix(result.route, result.errors);
        if (fixResult.success) {
          result.fixAttempted = true;
          result.fixResult = fixResult;
        }
      }

      await page.close();
      return result;

    } catch (error) {
      result.errors.push({
        type: 'validation-continuation-error',
        message: `页面验证继续过程出错: ${error.message}`,
        stack: error.stack
      });
      result.loadTime = Date.now() - startTime;
      await page.close();
      return result;
    }
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ');

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe', // 总是使用 pipe 以便监听输出
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      });

      let serverReady = false;
      let output = '';
      let detectedPort = null;

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString();
          output += text;

          if (this.options.verbose) {
            console.log(text);
          }

          // 添加更多调试信息
          // if (this.options.verbose && !serverReady) {
          //   console.log(chalk.cyan(`   🔍 检查文本: "${text.trim()}"`));
          //   console.log(chalk.cyan(`   🔍 包含 'Local:': ${text.includes('Local:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'localhost:': ${text.includes('localhost:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'App running at': ${text.includes('App running at')}`));
          // }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`));
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/);
            if (portMatch) {
              detectedPort = parseInt(portMatch[1]);
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`));
              }

              // 验证服务器是否真的可以访问
              this.verifyServerWithRetry(detectedPort, resolve, reject);
            }
          }
        });
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString();
          if (this.options.verbose) {
            console.error(chalk.red(text));
          }
        });
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`));
      });

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`));
        }
      }, this.options.waitForServer);
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 确保浏览器可用
   */
  async ensureBrowser() {
    try {
      const selectedBrowser = await this.browserDetector.ensureBrowser();

      // 如果选择的是系统浏览器，设置可执行文件路径
      if (selectedBrowser.type !== 'puppeteer') {
        this.options.executablePath = selectedBrowser.executablePath;
      }

      // 设置浏览器类型以便后续创建正确的适配器
      this.selectedBrowserType = selectedBrowser.type;

      return selectedBrowser;
    } catch (error) {
      throw new Error(`浏览器检测失败: ${error.message}`);
    }
  }

  /**
   * 创建浏览器自动化实例
   */
  async createBrowserAutomation() {
    try {
      // 如果已经有选择的浏览器类型，使用它
      if (this.selectedBrowserType) {
        return browserFactory.createBrowserAutomation(this.selectedBrowserType);
      }

      // 否则自动选择最佳的浏览器
      const preferredTypes = ['puppeteer', 'playwright-chromium', 'playwright-firefox', 'playwright-webkit'];
      return await browserFactory.createConfiguredBrowserAutomation({
        preferredTypes,
        autoSelect: true
      });
    } catch (error) {
      // 如果自动选择失败，回退到默认的 Puppeteer
      console.log(chalk.yellow(`⚠️  自动选择浏览器失败，使用默认 Puppeteer: ${error.message}`));
      return browserFactory.createBrowserAutomation('puppeteer');
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    // 确保浏览器可用
    await this.ensureBrowser();

    const launchOptions = {
      headless: this.options.headless === 'new' ? 'new' : this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    };

    // 如果检测到系统浏览器，使用它
    if (this.options.executablePath) {
      launchOptions.executablePath = this.options.executablePath;
    }

    try {
      // 使用抽象层启动浏览器
      this.browserAutomation = await this.createBrowserAutomation();
      this.browser = await this.browserAutomation.launch(launchOptions);
      console.log(chalk.green('✅ 浏览器启动成功'));
    } catch (error) {
      // 如果启动失败，尝试使用新的 headless 模式
      if (!launchOptions.headless || launchOptions.headless === true) {
        console.log(chalk.yellow('⚠️  尝试使用新的 headless 模式...'));
        launchOptions.headless = 'new';
        this.browser = await this.browserAutomation.launch(launchOptions);
        console.log(chalk.green('✅ 浏览器启动成功 (新 headless 模式)'));
      } else {
        throw error;
      }
    }
  }

  /**
   * 构建页面URL
   */
  buildPageUrl(route) {
    if (this.options.routerMode === 'hash') {
      // Hash模式: http://localhost:9527/#/example/list
      return `${this.baseUrl}/#${route.path}`;
    } else {
      // History模式: http://localhost:9527/example/list
      return `${this.baseUrl}${route.path}`;
    }
  }

  /**
   * 智能等待页面准备就绪
   */
  async waitForPageReady(page) {
    const maxWaitTime = 5000; // 最大等待5秒
    const checkInterval = 200; // 每200ms检查一次
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // 检查页面是否已经准备就绪
        const isReady = await page.evaluate(() => {
          // 检查DOM是否加载完成
          if (document.readyState !== 'complete') {
            return false;
          }

          // 检查是否有Vue应用
          const hasVue = !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));

          // 检查是否有明显的加载指示器
          const loadingElements = document.querySelectorAll([
            '.loading',
            '.spinner',
            '.loader',
            '[class*="loading"]',
            '[class*="spinner"]'
          ].join(','));

          const hasVisibleLoading = Array.from(loadingElements).some(el => {
            const style = window.getComputedStyle(el);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          });

          // 如果有Vue应用且没有可见的加载指示器，认为页面准备就绪
          return hasVue && !hasVisibleLoading;
        });

        if (isReady) {
          if (this.options.verbose) {
            console.log(chalk.gray(`    ✅ 页面准备就绪 (等待时间: ${waitTime}ms)`));
          }
          return;
        }

        // 等待一段时间后再次检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;

      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  页面准备检查出错: ${error.message}`));
        }
        break;
      }
    }

    // 如果超时，使用固定等待时间
    if (waitTime >= maxWaitTime) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  页面准备检查超时，使用固定等待时间`));
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  /**
   * 为页面截图
   */
  async takeScreenshot(page, route) {
    try {
      // 检查当前页面URL是否有效
      const currentUrl = page.url();
      if (!currentUrl || currentUrl === 'about:blank' || currentUrl.startsWith('chrome-error://') || currentUrl.startsWith('edge-error://')) {
        throw new Error(`无法对错误页面截图: ${currentUrl}`);
      }

      // 确保截图目录存在
      const screenshotDir = path.join(this.projectPath, 'validation-reports', 'screenshots');
      await fs.ensureDir(screenshotDir);

      // 生成截图文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const routeName = route.path.replace(/[\/\?#]/g, '_').replace(/^_/, '') || 'root';
      const filename = `${routeName}_${timestamp}.png`;
      const screenshotPath = path.join(screenshotDir, filename);

      // 设置视口大小
      try {
        await page.setViewport({ width: 1280, height: 800 });
      } catch (viewportError) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  设置视口失败: ${viewportError.message}`));
        }
        // 继续尝试截图，即使视口设置失败
      }

      // 等待页面稳定
      await this.sleep(1000);

      // 截图
      await page.screenshot({
        path: screenshotPath,
        fullPage: true,
        type: 'png'
      });

      return screenshotPath;
    } catch (error) {
      // 增强错误信息
      const currentUrl = page.url();
      throw new Error(`截图失败 (URL: ${currentUrl}): ${error.message}`);
    }
  }

  generateReport() {
    const totalPages = this.validationResults.length;
    const successfulPages = this.validationResults.filter(r => r.success).length;
    const failedPages = this.validationResults.filter(r => !r.success);

    // 收集所有错误进行 FAQ 分析
    const allErrors = [];
    const routeErrors = [];

    // 收集导航统计信息
    let totalNavigations = 0;
    let pagesWithRedirects = 0;
    let loginRedirects = 0;
    let errorPageRedirects = 0;
    let httpRedirects = 0;

    for (const result of this.validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }

      // 收集路由错误
      if (result.loginConsoleErrors && result.loginConsoleErrors.length > 0) {
        routeErrors.push(...result.loginConsoleErrors);
        // 将路由错误也加入到总错误列表中
        allErrors.push(...result.loginConsoleErrors);
      }

      // 收集导航历史统计
      if (result.navigationHistory && result.navigationHistory.length > 0) {
        const navigations = result.navigationHistory.length - 1; // 减去初始页面
        if (navigations > 0) {
          totalNavigations += navigations;
          pagesWithRedirects++;

          // 统计不同类型的跳转
          for (const nav of result.navigationHistory) {
            if (nav.isLoginRedirect) {
              loginRedirects++;
            }
            if (nav.isErrorPage) {
              errorPageRedirects++;
            }
            if (nav.type === 'redirect') {
              httpRedirects++;
            }
          }
        }
      }
    }

    // 进行 FAQ 分析
    const faqAnalysis = this.faqHelper.analyzeErrors(allErrors);

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0,
        routeErrors: routeErrors.length, // 添加路由错误统计
        // 添加导航统计
        navigation: {
          totalNavigations: totalNavigations,
          pagesWithRedirects: pagesWithRedirects,
          loginRedirects: loginRedirects,
          errorPageRedirects: errorPageRedirects,
          httpRedirects: httpRedirects
        }
      },
      results: this.validationResults,
      failedPages: failedPages,
      routeErrors: routeErrors, // 添加路由错误详情
      faqAnalysis: faqAnalysis,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 打印验证摘要
   */
  printSummary() {
    const report = this.generateReport();

    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    // 显示路由错误统计
    if (report.summary.routeErrors > 0) {
      console.log(chalk.yellow(`   路由错误: ${report.summary.routeErrors} 个`));
    }

    // 显示导航统计
    const nav = report.summary.navigation;
    if (nav.totalNavigations > 0) {
      console.log(chalk.cyan(`\n🔄 页面跳转统计:`));
      console.log(chalk.gray(`   总跳转次数: ${nav.totalNavigations}`));
      console.log(chalk.gray(`   有跳转的页面: ${nav.pagesWithRedirects}/${report.summary.total}`));

      if (nav.loginRedirects > 0) {
        console.log(chalk.yellow(`   登录重定向: ${nav.loginRedirects} 次`));
      }

      if (nav.httpRedirects > 0) {
        console.log(chalk.magenta(`   HTTP重定向: ${nav.httpRedirects} 次`));
      }

      if (nav.errorPageRedirects > 0) {
        console.log(chalk.red(`   错误页面跳转: ${nav.errorPageRedirects} 次`));
      }
    } else {
      console.log(chalk.green(`\n✅ 所有页面均无跳转，直接加载成功`));
    }

    if (report.failedPages.length > 0) {
      console.log(chalk.red('\n❌ 失败的页面:'));
      for (const failed of report.failedPages) {
        console.log(chalk.red(`   ${failed.route.path}: ${failed.errors.length} 个错误`));

        // 显示错误的详细信息（改进版）
        if (failed.errors.length > 0) {
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          formattedErrors.forEach((errorInfo, index) => {
            console.log(chalk.red(`     ${index + 1}. ${errorInfo.type}: ${errorInfo.summary}`));
            if (errorInfo.details && this.options.verbose) {
              console.log(chalk.gray(`        详情: ${errorInfo.details}`));
            }
          });

          if (failed.errors.length > formattedErrors.length) {
            console.log(chalk.gray(`     ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误`));
          }
        }
      }

      // 显示 FAQ 分析结果
      if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
        console.log(chalk.blue('\n💡 常见问题解决建议:'));
        console.log(chalk.gray(report.faqAnalysis.summary));

        // 显示前3个最相关的建议
        const topSuggestions = report.faqAnalysis.suggestions.slice(0, 3);
        for (const suggestion of topSuggestions) {
          const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
          console.log(chalk.yellow(`   • ${suggestion.solution} (${count} 个相关错误)`));

          if (suggestion.summary && suggestion.summary.cause) {
            console.log(chalk.gray(`     原因: ${suggestion.summary.cause}`));
          }
        }

        console.log(chalk.gray('\n   📖 详细解决方案请参考: docs/runtime-faq-summary.md'));
      }
    }

    // 显示路由错误详情
    if (report.routeErrors && report.routeErrors.length > 0) {
      console.log(chalk.yellow('\n🚨 路由错误详情:'));
      for (const error of report.routeErrors) {
        console.log(chalk.yellow(`   - [${error.phase}] ${error.message.substring(0, 150)}${error.message.length > 150 ? '...' : ''}`));
        if (this.options.verbose && error.timestamp) {
          console.log(chalk.gray(`     时间: ${error.timestamp}`));
        }
      }
      console.log(chalk.gray('\n   💡 提示: 路由错误通常需要修复路由配置文件'));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      if (this.devServer && !this.devServer.killed) {
        this.devServer.kill('SIGTERM');

        // 等待进程结束
        await new Promise((resolve) => {
          this.devServer.on('exit', resolve);
          setTimeout(resolve, 5000); // 5秒超时
        });
      }

      if (this.runtimeErrorHandler) {
        // 清理运行时错误处理器
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 尝试修复页面错误
   */
  async attemptPageErrorFix(route, errors) {
    try {
      if (!this.buildFixAgent) {
        return { success: false, error: 'BuildFixAgent 未初始化' };
      }

      // 过滤出可以修复的错误（排除登录、权限、网络等非代码错误）
      const fixableErrors = this.errorAnalyzer.filterFixableErrors(errors);

      if (fixableErrors.length === 0) {
        return {
          success: false,
          error: '没有可修复的代码错误',
          filteredCount: errors.length - fixableErrors.length
        };
      }

      // 构建错误上下文信息
      const errorContext = this.buildPageErrorContext(route, fixableErrors);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 分析页面错误: ${route.path}`));
        console.log(chalk.gray(`    总错误数量: ${errors.length}, 可修复错误: ${fixableErrors.length}`));
      }

      // 分析错误并确定需要修复的文件
      const analysisResult = await this.buildFixAgent.errorAnalyzer.analyzeBuildErrors(errorContext.buildOutput, 1);

      if (!analysisResult.success || !analysisResult.filesToFix || analysisResult.filesToFix.length === 0) {
        return {
          success: false,
          error: '无法确定需要修复的文件',
          analysisResult
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📁 需要修复的文件: ${analysisResult.filesToFix.join(', ')}`));
      }

      // 执行文件修复
      const fixResult = await this.buildFixAgent.fixFiles(
        analysisResult.filesToFix,
        errorContext.buildOutput,
        1
      );

      return {
        success: fixResult.success,
        filesModified: fixResult.filesModified || 0,
        totalFiles: fixResult.totalFiles || 0,
        errors: fixResult.errors,
        analysisResult,
        fixResult
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * 构建页面错误上下文信息
   */
  buildPageErrorContext(route, errors) {
    // 使用路由解析器推断可能的组件文件路径
    let suggestedFiles = [];
    let routeComponentInfo = {};
    if (this.routeParser) {
      const errorMessages = errors.map(error => {
        if (typeof error === 'string') {
          return error;
        } else if (error.message) {
          return `${error.type || 'Error'}: ${error.message}`;
        } else {
          return JSON.stringify(error);
        }
      });

      const errorMessage = errorMessages.join(' ');
      suggestedFiles = this.routeParser.inferComponentPaths(route.path, errorMessage);

      const directComponent = this.routeParser.getComponentPathByRoute(route.path);
      if (directComponent) {
        routeComponentInfo.directMapping = directComponent;
      }

      const allMappings = this.routeParser.getRouteComponentMap();
      routeComponentInfo.relatedMappings = this.findRelatedRouteMappings(route.path, allMappings);
    }

    const routeInfo = this.buildRouteContextInfo(route);

    const errorContext = {
      route,
      errors,
      timestamp: new Date().toISOString(),
      suggestedFiles,
      routeComponentInfo,
      routeInfo
    };

    const buildOutput = this.buildErrorAnalyzer.formatRuntimeErrorAsBuildOutput(errorContext);

    return {
      buildOutput,
      route,
      errors,
      errorCount: errors.length,
      timestamp: new Date().toISOString(),
      suggestedFiles, // 添加推荐的文件列表
      routeComponentInfo, // 添加路由组件信息
      routeInfo // 添加路由上下文信息
    };
  }

  /**
   * 构建路由上下文信息
   */
  buildRouteContextInfo(route) {
    const info = [];

    info.push(`路径: ${route.path}`);
    if (route.name) info.push(`名称: ${route.name}`);
    if (route.component) {
      if (typeof route.component === 'object') {
        info.push(`组件类型: ${route.component.type || 'unknown'}`);
        if (route.component.source) {
          info.push(`组件源: ${route.component.source}`);
        }
      } else {
        info.push(`组件: ${route.component}`);
      }
    }
    if (route.meta && Object.keys(route.meta).length > 0) {
      info.push(`元信息: ${JSON.stringify(route.meta)}`);
    }
    if (route.children && route.children.length > 0) {
      info.push(`子路由数量: ${route.children.length}`);
    }

    return info.join('\n');
  }

  /**
   * 查找相关的路由映射
   */
  findRelatedRouteMappings(currentPath, allMappings) {
    const related = [];
    const pathSegments = currentPath.split('/').filter(Boolean);

    for (const [routePath, component] of allMappings) {
      if (routePath === currentPath) continue;

      const routeSegments = routePath.split('/').filter(Boolean);

      // 查找父路由或同级路由
      if (this.isRelatedRoute(pathSegments, routeSegments)) {
        related.push({ route: routePath, component });
      }
    }

    return related.slice(0, 5); // 限制数量
  }

  /**
   * 判断是否是相关路由
   */
  isRelatedRoute(currentSegments, routeSegments) {
    // 父路由关系
    if (routeSegments.length < currentSegments.length) {
      return routeSegments.every((segment, index) => segment === currentSegments[index]);
    }

    // 同级路由关系
    if (routeSegments.length === currentSegments.length) {
      const commonSegments = routeSegments.filter((segment, index) => segment === currentSegments[index]);
      return commonSegments.length >= Math.max(1, routeSegments.length - 1);
    }

    return false;
  }

  /**
   * 检查页面错误（用于重新验证）
   */
  async checkPageErrors(page) {
    const errors = [];

    try {
      // 检查页面错误数据
      const errorData = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 收集所有错误
      errors.push(...errorData.pageErrors.map(e => ({ type: 'page-error', message: e.message })));
      errors.push(...errorData.vueErrors.map(e => ({ type: 'vue-error', message: e.message })));
      errors.push(...errorData.consoleErrors.filter(e => e.level === 'error').map(e => ({ type: 'console-error', message: e.message })));

      // 检查DOM中的错误元素
      const domErrors = await page.evaluate(() => {
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '.error-message',
          '.runtime-error'
        ].join(','));

        const errors = [];
        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError')
          )) {
            errors.push({
              type: 'dom-error',
              message: text.substring(0, 200)
            });
          }
        }
        return errors;
      });

      errors.push(...domErrors);

    } catch (error) {
      errors.push({
        type: 'check-error',
        message: `检查页面错误时出错: ${error.message}`
      });
    }

    return errors;
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取验证结果
   */
  getResults() {
    return this.validationResults;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const report = this.generateReport();

    // 生成 Markdown 报告
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));
  }

  /**
   * 生成 Markdown 格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n`;

    // 添加导航统计
    const nav = report.summary.navigation;
    if (nav.totalNavigations > 0) {
      markdown += `\n### 🔄 页面跳转统计\n\n`;
      markdown += `- 总跳转次数: ${nav.totalNavigations}\n`;
      markdown += `- 有跳转的页面: ${nav.pagesWithRedirects}/${report.summary.total}\n`;

      if (nav.loginRedirects > 0) {
        markdown += `- 登录重定向: ${nav.loginRedirects} 次\n`;
      }

      if (nav.httpRedirects > 0) {
        markdown += `- HTTP重定向: ${nav.httpRedirects} 次\n`;
      }

      if (nav.errorPageRedirects > 0) {
        markdown += `- 错误页面跳转: ${nav.errorPageRedirects} 次\n`;
      }
    } else {
      markdown += `\n### ✅ 页面跳转\n\n`;
      markdown += `所有页面均无跳转，直接加载成功\n`;
    }

    markdown += `\n`;

    if (report.failedPages.length > 0) {
      markdown += `## 失败的页面\n\n`;
      for (const failed of report.failedPages) {
        markdown += `### ${failed.route.path}\n\n`;
        markdown += `- URL: ${failed.url}\n`;
        markdown += `- 加载时间: ${failed.loadTime}ms\n`;

        if (failed.errors.length > 0) {
          markdown += `- 错误:\n`;
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          for (const errorInfo of formattedErrors) {
            markdown += `  - **${errorInfo.type}**: ${errorInfo.summary}\n`;
            if (errorInfo.details) {
              markdown += `    \`\`\`\n    ${errorInfo.details}\n    \`\`\`\n`;
            }
          }

          if (failed.errors.length > formattedErrors.length) {
            markdown += `  - ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误\n`;
          }
        }

        if (failed.warnings.length > 0) {
          markdown += `- 警告:\n`;
          for (const warning of failed.warnings) {
            markdown += `  - ${warning}\n`;
          }
        }

        markdown += `\n`;
      }
    }

    // 添加 FAQ 分析结果
    if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
      markdown += `## 💡 常见问题解决建议\n\n`;
      markdown += `${report.faqAnalysis.summary}\n\n`;

      for (const suggestion of report.faqAnalysis.suggestions) {
        const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
        markdown += `### ${suggestion.solution} (${count} 个相关错误)\n\n`;

        if (suggestion.summary) {
          if (suggestion.summary.cause) {
            markdown += `**根本原因**: ${suggestion.summary.cause}\n\n`;
          }

          if (suggestion.summary.steps.length > 0) {
            markdown += `**解决步骤**:\n`;
            for (const step of suggestion.summary.steps) {
              markdown += `1. ${step}\n`;
            }
            markdown += `\n`;
          }
        }
      }

      markdown += `> 📖 详细解决方案请参考: [docs/runtime-faq-summary.md](docs/runtime-faq-summary.md)\n\n`;
    }

    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }
}

module.exports = PageValidator;
