const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;

/**
 * RouteConfigValidator - 路由配置验证器
 * 
 * 功能：
 * 1. 静态验证路由配置文件的正确性
 * 2. 检查路由路径格式（必须以 / 开头）
 * 3. 检查重复路由
 * 4. 检查无效的路由配置
 * 5. 提供修复建议
 */
class RouteConfigValidator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      autoFix: false,
      ...options
    };
    
    this.validationErrors = [];
    this.validationWarnings = [];
    this.routeFiles = [];
  }

  /**
   * 验证所有路由配置
   */
  async validateRouteConfigs() {
    console.log(chalk.blue('🔍 开始验证路由配置...'));
    
    try {
      // 1. 查找路由文件
      await this.findRouteFiles();
      
      if (this.routeFiles.length === 0) {
        throw new Error('未找到路由配置文件');
      }
      
      // 2. 验证每个路由文件
      for (const routeFile of this.routeFiles) {
        await this.validateRouteFile(routeFile);
      }
      
      // 3. 生成验证报告
      const report = this.generateValidationReport();
      
      if (this.validationErrors.length === 0) {
        console.log(chalk.green('✅ 路由配置验证通过'));
      } else {
        console.log(chalk.red(`❌ 发现 ${this.validationErrors.length} 个路由配置错误`));
        
        if (this.options.verbose) {
          this.printValidationDetails();
        }
      }
      
      return report;
      
    } catch (error) {
      console.error(chalk.red(`❌ 路由配置验证失败: ${error.message}`));
      return {
        success: false,
        errors: [error.message],
        warnings: [],
        fixSuggestions: []
      };
    }
  }

  /**
   * 查找路由配置文件
   */
  async findRouteFiles() {
    const routerPaths = [
      'src/router/index.js',
      'src/router/index.ts',
      'router/index.js',
      'router/index.ts',
      'src/router.js',
      'src/router.ts'
    ];

    for (const routerPath of routerPaths) {
      const fullPath = path.join(this.projectPath, routerPath);
      
      if (await fs.pathExists(fullPath)) {
        this.routeFiles.push({
          path: routerPath,
          fullPath: fullPath
        });
        
        if (this.options.verbose) {
          console.log(chalk.gray(`   找到路由文件: ${routerPath}`));
        }
      }
    }

    // 递归查找 router 目录下的其他文件
    const routerDir = path.join(this.projectPath, 'src/router');
    if (await fs.pathExists(routerDir)) {
      await this.findAdditionalRouterFiles(routerDir);
    }
  }

  /**
   * 查找额外的路由文件
   */
  async findAdditionalRouterFiles(routerDir) {
    try {
      const files = await fs.readdir(routerDir, { withFileTypes: true });
      
      for (const file of files) {
        const fullPath = path.join(routerDir, file.name);
        
        if (file.isDirectory()) {
          await this.findAdditionalRouterFiles(fullPath);
        } else if (file.isFile() && (file.name.endsWith('.js') || file.name.endsWith('.ts'))) {
          const relativePath = path.relative(this.projectPath, fullPath);
          
          // 避免重复添加主路由文件
          if (!this.routeFiles.some(rf => rf.fullPath === fullPath)) {
            this.routeFiles.push({
              path: relativePath,
              fullPath: fullPath
            });
            
            if (this.options.verbose) {
              console.log(chalk.gray(`   找到额外路由文件: ${relativePath}`));
            }
          }
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  读取路由目录失败: ${error.message}`));
      }
    }
  }

  /**
   * 验证单个路由文件
   */
  async validateRouteFile(routeFile) {
    try {
      const content = await fs.readFile(routeFile.fullPath, 'utf8');
      
      if (this.options.verbose) {
        console.log(chalk.gray(`   验证文件: ${routeFile.path}`));
      }
      
      // 解析文件并验证路由配置
      await this.parseAndValidateRoutes(content, routeFile);
      
    } catch (error) {
      this.validationErrors.push({
        file: routeFile.path,
        type: 'parse-error',
        message: `文件解析失败: ${error.message}`,
        line: null,
        column: null
      });
    }
  }

  /**
   * 解析并验证路由配置
   */
  async parseAndValidateRoutes(content, routeFile) {
    try {
      const ast = parse(content, {
        sourceType: 'module',
        plugins: [
          'jsx',
          'typescript',
          'decorators-legacy',
          'classProperties',
          'objectRestSpread',
          'asyncGenerators',
          'functionBind',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'dynamicImport'
        ]
      });

      const self = this;
      
      // 遍历 AST 查找路由定义并验证
      traverse(ast, {
        // 验证路由对象
        ObjectExpression(path) {
          const obj = path.node;
          const pathProperty = obj.properties.find(prop =>
            prop.key && prop.key.name === 'path' && prop.value.type === 'StringLiteral'
          );

          if (pathProperty) {
            const routePath = pathProperty.value.value;
            const line = pathProperty.loc ? pathProperty.loc.start.line : null;
            const column = pathProperty.loc ? pathProperty.loc.start.column : null;

            // 检查是否有children属性，判断是否为父路由
            const hasChildren = obj.properties.some(prop =>
              prop.key && prop.key.name === 'children'
            );

            // 验证路由路径格式
            self.validateRoutePath(routePath, routeFile, line, column, { hasChildren });
          }
        }
      });
      
    } catch (error) {
      this.validationErrors.push({
        file: routeFile.path,
        type: 'ast-parse-error',
        message: `AST解析失败: ${error.message}`,
        line: null,
        column: null
      });
    }
  }

  /**
   * 验证路由路径格式
   */
  validateRoutePath(routePath, routeFile, line, column, context = {}) {
    // 检查是否是外部链接
    if (this.isExternalUrl(routePath)) {
      // 外部链接不需要以 / 开头，但需要特殊处理
      this.validationWarnings.push({
        file: routeFile.path,
        type: 'external-url-path',
        message: `外部链接作为路由路径: "${routePath}"`,
        routePath: routePath,
        line: line,
        column: column
      });
      return;
    }

    // 检查是否是通配符路由
    if (routePath === '*') {
      this.validationErrors.push({
        file: routeFile.path,
        type: 'wildcard-route-format',
        message: `Vue Router 4 wildcard route: "${routePath}" should be "/:pathMatch(.*)*".`,
        routePath: routePath,
        suggestedFix: '/:pathMatch(.*)*',
        line: line,
        column: column
      });
      return;
    }

    // 检查路径是否以 / 开头（除了空字符串）
    if (routePath !== '' && !routePath.startsWith('/')) {
      // 对于有children的父路由，给出更详细的说明
      let message = `Route paths should start with a "/": "${routePath}" should be "/${routePath}".`;
      let suggestedFix = `/${routePath}`;

      if (context.hasChildren) {
        message += ` (这是一个父路由，包含子路由)`;
      }

      this.validationErrors.push({
        file: routeFile.path,
        type: 'invalid-path-format',
        message: message,
        routePath: routePath,
        suggestedFix: suggestedFix,
        line: line,
        column: column,
        context: context
      });
    }
    
    // 检查其他路径格式问题
    if (routePath.includes('//')) {
      this.validationWarnings.push({
        file: routeFile.path,
        type: 'double-slash',
        message: `路径包含双斜杠: "${routePath}"`,
        routePath: routePath,
        line: line,
        column: column
      });
    }
    
    if (routePath.endsWith('/') && routePath !== '/') {
      this.validationWarnings.push({
        file: routeFile.path,
        type: 'trailing-slash',
        message: `路径以斜杠结尾: "${routePath}"`,
        routePath: routePath,
        line: line,
        column: column
      });
    }
  }

  /**
   * 检查是否是外部URL
   */
  isExternalUrl(path) {
    return /^https?:\/\//.test(path) || /^\/\//.test(path);
  }

  /**
   * 生成验证报告
   */
  generateValidationReport() {
    const fixSuggestions = this.generateFixSuggestions();
    
    return {
      success: this.validationErrors.length === 0,
      errors: this.validationErrors,
      warnings: this.validationWarnings,
      fixSuggestions: fixSuggestions,
      summary: {
        totalFiles: this.routeFiles.length,
        errorCount: this.validationErrors.length,
        warningCount: this.validationWarnings.length
      }
    };
  }

  /**
   * 生成修复建议
   */
  generateFixSuggestions() {
    const suggestions = [];

    // 按文件分组错误
    const errorsByFile = new Map();

    for (const error of this.validationErrors) {
      if (!errorsByFile.has(error.file)) {
        errorsByFile.set(error.file, []);
      }
      errorsByFile.get(error.file).push(error);
    }

    // 为每个文件生成修复建议
    for (const [file, errors] of errorsByFile) {
      const pathFormatErrors = errors.filter(e => e.type === 'invalid-path-format');
      const wildcardErrors = errors.filter(e => e.type === 'wildcard-route-format');

      if (pathFormatErrors.length > 0) {
        suggestions.push({
          file: file,
          type: 'fix-path-format',
          description: `修复 ${pathFormatErrors.length} 个路径格式错误`,
          changes: pathFormatErrors.map(error => ({
            line: error.line,
            column: error.column,
            from: `"${error.routePath}"`,
            to: `"${error.suggestedFix}"`,
            originalPath: error.routePath,
            fixedPath: error.suggestedFix
          }))
        });
      }

      if (wildcardErrors.length > 0) {
        suggestions.push({
          file: file,
          type: 'fix-wildcard-route',
          description: `修复 ${wildcardErrors.length} 个通配符路由`,
          changes: wildcardErrors.map(error => ({
            line: error.line,
            column: error.column,
            from: `"${error.routePath}"`,
            to: `"${error.suggestedFix}"`,
            originalPath: error.routePath,
            fixedPath: error.suggestedFix
          }))
        });
      }
    }

    return suggestions;
  }

  /**
   * 打印验证详情
   */
  printValidationDetails() {
    if (this.validationErrors.length > 0) {
      console.log(chalk.red('\n❌ 路由配置错误:'));
      for (const error of this.validationErrors) {
        const location = error.line ? ` (行 ${error.line})` : '';
        console.log(chalk.red(`   - ${error.file}${location}: ${error.message}`));
        if (error.suggestedFix) {
          console.log(chalk.yellow(`     建议修复: "${error.routePath}" → "${error.suggestedFix}"`));
        }
      }
    }
    
    if (this.validationWarnings.length > 0) {
      console.log(chalk.yellow('\n⚠️  路由配置警告:'));
      for (const warning of this.validationWarnings) {
        const location = warning.line ? ` (行 ${warning.line})` : '';
        console.log(chalk.yellow(`   - ${warning.file}${location}: ${warning.message}`));
      }
    }
  }
}

module.exports = RouteConfigValidator;
